# Mail Auto v1 Production Launch Guide

## 🎯 Current Status & Next Steps

### ✅ Completed Foundation
- ✅ Multi-tenant Azure integration with Key Vault
- ✅ Development environment with personal account
- ✅ Subscription management system with limits (Starter/Business/Enterprise)
- ✅ Customer onboarding flow with Azure consent
- ✅ Web portal with admin/customer interfaces
- ✅ Document processing with subscription limits
- ✅ Mailbox management with plan restrictions

### 🚀 Week-by-Week Production Launch Plan

## **WEEK 1: Azure Production Infrastructure Setup**

### Day 1-2: Create Production Azure Resources

#### Step 1: Create Resource Group (Like a folder for all your Azure stuff)
```bash
# Open PowerShell as Administrator and run:
az login
az group create --name "mail-auto-prod-rg" --location "West Europe"
```

#### Step 2: Create Production Key Vault (Secure storage for customer passwords)
```bash
# This creates a secure vault to store customer email passwords
az keyvault create \
  --name "mail-auto-prod-vault" \
  --resource-group "mail-auto-prod-rg" \
  --location "West Europe" \
  --sku standard
```

#### Step 3: Create Production App Registration (Your app's ID card with Microsoft)
```bash
# This tells Microsoft "Mail Auto is allowed to access customer emails"
az ad app create \
  --display-name "Mail-Auto-Production" \
  --web-redirect-uris "https://yourdomain.com/auth/callback" \
  --required-resource-accesses '[
    {
      "resourceAppId": "00000003-0000-0000-c000-000000000000",
      "resourceAccess": [
        {"id": "e1fe6dd8-ba31-4d61-89e7-88639da4683d", "type": "Scope"},
        {"id": "024d486e-b451-40bb-833d-3e66d98c5c73", "type": "Scope"},
        {"id": "570282fd-fa5c-430d-a7fd-fc8dc98a9dca", "type": "Scope"}
      ]
    }
  ]'
```

**What this gives you access to when customers click the link:**
- ✅ **Full mailbox access** - Read all emails in specified mailboxes
- ✅ **OneDrive access** - Read files in their OneDrive (if they grant permission)
- ✅ **Send emails** - Send notifications from their mailboxes
- ✅ **Create folders** - Organize processed documents

### Day 3: Configure App Permissions (Tell Microsoft exactly what you need)

#### Step 4: Add Required Permissions
1. Go to Azure Portal → App Registrations → Mail-Auto-Production
2. Click "API permissions" → "Add a permission"
3. Select "Microsoft Graph" → "Delegated permissions"
4. Add these permissions:
   - `Mail.Read` - Read customer emails
   - `Mail.Send` - Send notifications from customer mailbox
   - `Files.Read.All` - Read OneDrive files
   - `Files.ReadWrite.All` - Save processed documents to OneDrive
   - `User.Read` - Get customer basic info

#### Step 5: Create Client Secret (Password for your app)
1. Go to "Certificates & secrets" → "New client secret"
2. Description: "Production Secret"
3. Expires: 24 months
4. **SAVE THE SECRET VALUE** - you can't see it again!

### Day 4-5: Update Your Code for Production

#### Step 6: Update .env File
```env
# Production Environment Settings
MAIL_AUTO_ENVIRONMENT=production
PROD_KEY_VAULT_URL=https://mail-auto-prod-vault.vault.azure.net/
PROD_TENANT_ID=your-azure-tenant-id
PROD_CLIENT_ID=your-prod-app-client-id
PROD_CLIENT_SECRET=your-prod-app-client-secret
PROD_REDIRECT_URI=https://yourdomain.com/auth/callback
PROD_USE_KEY_VAULT=true
```

## **WEEK 2: Customer Registration & Authentication System**

### Day 1-2: Build Customer Registration System

#### Step 7: Create Customer Registration Page
Create a simple web form where customers enter:
- Company name
- Email address
- Choose subscription plan (Starter/Business/Enterprise)

#### Step 8: Build Registration Backend
```python
# Add to web_server.py
@app.route('/api/register', methods=['POST'])
def register_customer():
    data = request.get_json()

    # Get customer info
    company_name = data.get('company_name')
    email = data.get('email')
    plan = data.get('subscription_plan', 'starter')

    # Create tenant name (safe for folders)
    tenant_name = company_name.lower().replace(' ', '-').replace('.', '')

    # Generate onboarding link
    onboarding_url = f"https://yourdomain.com/onboard/{tenant_name}"

    # Store customer registration (you'll add database later)
    # For now, just return the onboarding link

    return jsonify({
        'success': True,
        'onboarding_url': onboarding_url,
        'message': f'Please complete setup at: {onboarding_url}'
    })
```

### Day 3-4: Customer Onboarding Flow

#### Step 9: What Happens When Customer Clicks Onboarding Link

**Customer Journey (Simple Explanation):**
1. **Customer clicks link** → `https://yourdomain.com/onboard/acme-corp`
2. **Your system redirects them to Microsoft** → "ACME Corp wants to access your emails"
3. **Customer sees permission screen** → Lists exactly what you can access
4. **Customer clicks "Accept"** → Microsoft sends them back to your app
5. **Your app gets access token** → Can now read their emails
6. **You store their credentials** → Safely in Azure Key Vault
7. **Customer gets dashboard access** → Can configure mailboxes and document types

#### Step 10: Configure Customer Mailbox Selection
When customer completes onboarding, they see a setup page:

```
📧 Which mailboxes should Mail Auto monitor?
☑️ <EMAIL>
☑️ <EMAIL>
☑️ <EMAIL>
☐ <EMAIL>
☐ <EMAIL>

📄 Which document types should we process?
☑️ Invoices → Save to: Invoices/{year}/{company}
☑️ Contracts → Save to: Contracts/{year}/{company}
☑️ Certificates → Save to: Certificates/{year}/{company}
☐ Purchase Orders
☐ Legal Documents

💾 Where should we save processed documents?
○ OneDrive Business
○ SharePoint
○ Local folders
```

### Day 5: Customer Authentication System

#### Step 11: Replace dev_mode with Real Customer Auth
```python
# Update web_server.py endpoints to use customer authentication
from core.customer_auth import get_customer_tenant_name

@app.route('/api/subscription/info')
def get_subscription_info():
    # Get customer from JWT token instead of dev_mode
    tenant_name = get_customer_tenant_name(request)
    if not tenant_name:
        return jsonify({'error': 'Please log in'}), 401

    # Rest of your code stays the same
    subscription_manager = get_subscription_manager(tenant_name)
    return jsonify(subscription_manager.get_subscription_info())
```

## **WEEK 3: Customer Dashboard & Real Data Integration**

### Day 1-2: Customer Dashboard Real Data

#### Step 12: Show Real Customer Data (Not Placeholders)
```javascript
// Update web_portal/script.js to show real tenant data
async function loadCustomerDashboard() {
    // Get customer's JWT token from login
    const token = localStorage.getItem('customer_token');

    // Fetch real subscription info
    const subResponse = await fetch('/api/subscription/info', {
        headers: { 'Authorization': `Bearer ${token}` }
    });
    const subscription = await subResponse.json();

    // Show real plan and usage
    document.getElementById('current-plan').textContent = subscription.plan;
    document.getElementById('mailboxes-used').textContent =
        `${subscription.usage.current_mailboxes}/${subscription.limits.max_mailboxes}`;
    document.getElementById('documents-used').textContent =
        `${subscription.usage.documents_this_month}/${subscription.limits.max_documents_per_month}`;
}
```

#### Step 13: Customer Mailbox Configuration
```python
# Add endpoint for customer to configure their mailboxes
@app.route('/api/customer/mailboxes', methods=['GET', 'POST'])
def manage_customer_mailboxes():
    tenant_name = get_customer_tenant_name(request)
    if not tenant_name:
        return jsonify({'error': 'Authentication required'}), 401

    if request.method == 'GET':
        # Return customer's current mailbox configuration
        config = load_tenant_config(tenant_name)
        return jsonify(config.get('mailboxes', {}))

    elif request.method == 'POST':
        # Add new mailbox for customer
        data = request.get_json()
        mailbox_email = data.get('mailbox_email')

        # Check subscription limits
        subscription_manager = get_subscription_manager(tenant_name)
        if not subscription_manager.can_add_mailbox():
            return jsonify({'error': 'Mailbox limit reached. Please upgrade your plan.'}), 400

        # Add mailbox to customer's config
        # Your existing mailbox_manager code handles this
        return jsonify({'success': True})
```

### Day 3-4: Document Type Configuration

#### Step 14: Customer Document Type Setup
```python
# Let customers configure which document types to process
@app.route('/api/customer/document-types', methods=['GET', 'POST'])
def manage_document_types():
    tenant_name = get_customer_tenant_name(request)

    if request.method == 'GET':
        # Return available document types and customer's settings
        config = load_tenant_config(tenant_name)
        return jsonify({
            'available_types': ['invoices', 'contracts', 'certificates', 'purchase_orders'],
            'customer_settings': config.get('document_types', {})
        })

    elif request.method == 'POST':
        # Customer enables/disables document types
        data = request.get_json()
        doc_type = data.get('document_type')
        enabled = data.get('enabled', True)
        storage_path = data.get('storage_path', f'{doc_type}/{{year}}/{{company}}')

        # Update customer's config
        # If customer doesn't specify settings, use global defaults
        return jsonify({'success': True})
```

### Day 5: Test Customer Experience

#### Step 15: End-to-End Customer Test
1. **Register new test customer** → Use your registration form
2. **Complete onboarding** → Click onboarding link, grant permissions
3. **Configure mailboxes** → Select which mailboxes to monitor
4. **Set document types** → Choose what documents to process
5. **Test email processing** → Send test email, verify it gets processed
6. **Check subscription limits** → Try to exceed limits, verify blocking works

## **WEEK 4: Billing Integration & Production Deployment**

### Day 1-2: Stripe Billing Integration

#### Step 16: Setup Stripe Account
1. **Create Stripe account** → Go to stripe.com, sign up
2. **Get API keys** → Dashboard → Developers → API keys
3. **Create products** → Dashboard → Products → Add products for each plan

```python
# Add to your .env file
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
```

#### Step 17: Create Subscription Plans in Stripe
```python
# Run this once to create your plans in Stripe
import stripe
stripe.api_key = "sk_test_..."

# Create Starter Plan
starter_price = stripe.Price.create(
    unit_amount=2900,  # €29.00
    currency='eur',
    recurring={'interval': 'month'},
    product_data={'name': 'Mail Auto Starter Plan'}
)

# Create Business Plan
business_price = stripe.Price.create(
    unit_amount=9900,  # €99.00
    currency='eur',
    recurring={'interval': 'month'},
    product_data={'name': 'Mail Auto Business Plan'}
)

# Create Enterprise Plan
enterprise_price = stripe.Price.create(
    unit_amount=29900,  # €299.00
    currency='eur',
    recurring={'interval': 'month'},
    product_data={'name': 'Mail Auto Enterprise Plan'}
)
```

#### Step 18: Add Payment to Registration
```python
# Update registration to include payment
@app.route('/api/register-with-payment', methods=['POST'])
def register_with_payment():
    data = request.get_json()

    # Create Stripe customer
    customer = stripe.Customer.create(
        email=data['email'],
        name=data['company_name']
    )

    # Create subscription
    subscription = stripe.Subscription.create(
        customer=customer.id,
        items=[{'price': PLAN_PRICE_IDS[data['plan']]}],
        payment_behavior='default_incomplete',
        expand=['latest_invoice.payment_intent']
    )

    # Return client secret for payment
    return jsonify({
        'client_secret': subscription.latest_invoice.payment_intent.client_secret,
        'subscription_id': subscription.id
    })
```

### Day 3-4: Azure App Service Deployment

#### Step 19: Deploy to Azure App Service (Like putting your app on the internet)
```bash
# Create App Service Plan (like renting a server)
az appservice plan create \
  --name "mail-auto-plan" \
  --resource-group "mail-auto-prod-rg" \
  --sku B1 \
  --is-linux

# Create Web App (your website)
az webapp create \
  --resource-group "mail-auto-prod-rg" \
  --plan "mail-auto-plan" \
  --name "mail-auto-prod" \
  --runtime "PYTHON|3.11"
```

#### Step 20: Configure App Settings
```bash
# Set environment variables for your app
az webapp config appsettings set \
  --resource-group "mail-auto-prod-rg" \
  --name "mail-auto-prod" \
  --settings \
    MAIL_AUTO_ENVIRONMENT=production \
    PROD_KEY_VAULT_URL=https://mail-auto-prod-vault.vault.azure.net/ \
    STRIPE_SECRET_KEY=your_stripe_secret_key
```

### Day 5: Domain & SSL Setup

#### Step 21: Add Your Domain
```bash
# Add custom domain (replace yourdomain.com with your actual domain)
az webapp config hostname add \
  --webapp-name "mail-auto-prod" \
  --resource-group "mail-auto-prod-rg" \
  --hostname "yourdomain.com"

# Enable HTTPS (secure connection)
az webapp config ssl bind \
  --name "mail-auto-prod" \
  --resource-group "mail-auto-prod-rg" \
  --ssl-type SNI \
  --certificate-thumbprint YOUR_CERT_THUMBPRINT
```

## **WEEK 5: Beta Testing & Launch**

### Day 1-2: Beta Customer Onboarding

#### Step 22: Invite 5-10 Beta Customers
1. **Choose friendly customers** → People who won't mind if something breaks
2. **Send them registration link** → `https://yourdomain.com/register`
3. **Guide them through onboarding** → Be available to help
4. **Monitor their usage** → Watch for errors or issues

#### Step 23: Monitor Beta Customer Activity
```python
# Add monitoring dashboard for beta customers
@app.route('/admin/beta-monitoring')
def beta_monitoring():
    """Monitor beta customer activity"""
    beta_customers = get_beta_customers()

    stats = []
    for customer in beta_customers:
        tenant_stats = {
            'tenant_name': customer['tenant_name'],
            'emails_processed': get_processed_count(customer['tenant_name']),
            'last_activity': get_last_activity(customer['tenant_name']),
            'errors': get_error_count(customer['tenant_name']),
            'subscription_usage': get_subscription_usage(customer['tenant_name'])
        }
        stats.append(tenant_stats)

    return jsonify(stats)
```

### Day 3-4: Fix Issues & Gather Feedback

#### Step 24: Common Issues to Watch For
- **Authentication problems** → Customers can't log in
- **Permission errors** → Can't access their emails
- **Subscription limits** → Hitting limits unexpectedly
- **Document processing** → Files not being processed correctly
- **Notification delivery** → Emails not being sent

#### Step 25: Customer Feedback Collection
```python
# Add feedback endpoint
@app.route('/api/customer/feedback', methods=['POST'])
def submit_feedback():
    tenant_name = get_customer_tenant_name(request)
    data = request.get_json()

    feedback = {
        'tenant_name': tenant_name,
        'rating': data.get('rating'),
        'comments': data.get('comments'),
        'feature_requests': data.get('feature_requests'),
        'timestamp': datetime.now().isoformat()
    }

    # Store feedback (add to database later)
    store_customer_feedback(feedback)

    return jsonify({'success': True, 'message': 'Thank you for your feedback!'})
```

### Day 5: Launch Preparation

#### Step 26: Final Launch Checklist
- ✅ **All beta customers working** → No critical issues
- ✅ **Payment system tested** → Stripe subscriptions working
- ✅ **Monitoring in place** → Can see what's happening
- ✅ **Support system ready** → Can help customers quickly
- ✅ **Marketing materials** → Website, pricing page, documentation
- ✅ **Legal documents** → Terms of service, privacy policy

## **🎯 What Customers Get When They Click the Onboarding Link**

### **Full Access Permissions (Explained Simply):**

When a customer clicks your onboarding link and grants permissions, you get:

#### ✅ **Email Access**
- **Read all emails** in mailboxes they specify
- **Send notifications** from their mailboxes (like <EMAIL> sends invoice notifications)
- **Create email folders** to organize processed emails
- **Access email attachments** to process documents

#### ✅ **OneDrive/SharePoint Access**
- **Read files** from their OneDrive Business
- **Save processed documents** to organized folders
- **Create folder structures** like `Invoices/2024/ACME-Corp/`
- **Access shared documents** if they grant permission

#### ✅ **What They Control**
- **Which mailboxes to monitor** → They choose specific email addresses
- **Which document types to process** → They enable/disable invoice processing, contract processing, etc.
- **Where to save files** → OneDrive, SharePoint, or local folders
- **Notification recipients** → Who gets notified when documents are processed
- **Revoke access anytime** → Through Microsoft admin center

### **Customer Configuration Example:**
```
📧 Monitored Mailboxes:
✅ <EMAIL> → Process invoices, save to OneDrive/Invoices/
✅ <EMAIL> → Process contracts, save to OneDrive/Contracts/
❌ <EMAIL> → Not monitored

📄 Document Types:
✅ Invoices → Extract: amount, date, vendor → Notify: <EMAIL>
✅ Contracts → Extract: parties, dates, value → Notify: <EMAIL>
❌ Purchase Orders → Not processed

💾 Storage:
✅ OneDrive Business/Mail Auto/{doc_type}/{year}/{company}/
```

## **🚀 Quick Start Commands for Each Week**

### Week 1 Commands:
```bash
# Setup Azure infrastructure
az login
az group create --name "mail-auto-prod-rg" --location "West Europe"
az keyvault create --name "mail-auto-prod-vault" --resource-group "mail-auto-prod-rg"
```

### Week 2 Commands:
```bash
# Test customer registration
python web_server.py
# Visit: http://localhost:5000/register
```

### Week 3 Commands:
```bash
# Test customer dashboard
python Test/production_scenarios.py
```

### Week 4 Commands:
```bash
# Deploy to Azure
az webapp create --resource-group "mail-auto-prod-rg" --name "mail-auto-prod"
```

### Week 5 Commands:
```bash
# Monitor beta customers
python -c "from core.tracking import get_tracking_service; print('Beta customer stats:', get_tracking_service().get_tenant_stats())"
```

## **📞 Customer Support & Maintenance Plan**

### Support Levels by Plan:
- **Starter Plan (€29/month)**:
  - Email support only
  - 48-hour response time
  - Basic documentation access

- **Business Plan (€99/month)**:
  - Priority email support
  - Live chat during business hours
  - 24-hour response time
  - Video call setup assistance

- **Enterprise Plan (€299/month)**:
  - Dedicated support manager
  - Phone support
  - 4-hour response time
  - Custom integration assistance
  - Monthly check-in calls

### Monitoring & Alerts:
```python
# Set up alerts for common issues
ALERT_CONDITIONS = {
    'subscription_limit_reached': 'Customer hits 90% of document limit',
    'authentication_failure': 'Customer can\'t access their emails',
    'processing_errors': 'More than 5 failed document processing attempts',
    'payment_failure': 'Stripe subscription payment fails',
    'high_usage': 'Customer processes 10x normal volume'
}
```

## **🎉 Success Metrics & KPIs**

### Week 1 Success:
- ✅ Azure infrastructure created without errors
- ✅ Test customer can complete onboarding flow
- ✅ Subscription limits work correctly

### Week 2 Success:
- ✅ Customer registration form works
- ✅ Real customer data shows in dashboard
- ✅ Mailbox configuration saves correctly

### Week 3 Success:
- ✅ Customer can process real emails
- ✅ Documents saved to correct locations
- ✅ Notifications sent from customer mailboxes

### Week 4 Success:
- ✅ Payment processing works
- ✅ App deployed to Azure successfully
- ✅ Custom domain with HTTPS working

### Week 5 Success:
- ✅ 5+ beta customers actively using system
- ✅ No critical bugs reported
- ✅ Positive customer feedback
- ✅ Ready for marketing launch

---

## **🚀 Ready to Start? Your First Step:**

**This Week (Week 1):** Focus on Azure infrastructure setup. Start with Step 1 (Create Resource Group) and work through each step. Don't move to Week 2 until Week 1 is completely working.

**Need Help?** Each step has simple commands you can copy and paste. If something doesn't work, the error message will tell you what's wrong.

**Testing:** After each step, test that it works before moving to the next step. This way you catch problems early when they're easy to fix.

**Remember:** You're building a professional system that will handle real customer data and payments. Take your time and do each step carefully.

**Your customers will have full control** - they choose which mailboxes to monitor, which document types to process, and can revoke access anytime. You're building a service they'll trust with their business documents.
